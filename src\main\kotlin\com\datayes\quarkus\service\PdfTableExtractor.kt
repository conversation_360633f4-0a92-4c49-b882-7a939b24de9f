package com.datayes.quarkus.service

import com.datayes.llm.DoubaoLlm
import com.datayes.llm.LlmCallLogger
import com.datayes.llm.LlmPromptBuilder
import com.datayes.llm.LlmPromptBuilder.buildTableSelectionPrompt
import com.datayes.llm.extractTagContent
import com.datayes.model.SysColumnType
import com.datayes.model.promptColumnMetadata
import com.datayes.quarkus.client.TablesResponse
import com.datayes.util.mergeJsonListResults
import io.quarkus.logging.Log
import kotlin.collections.chunked
import kotlin.collections.forEach

object PdfTableExtractor {

    /**
     * 智能处理PDF表格 (Intelligent PDF Tables Processing)
     *
     * 通过使用目标表格名称与PDF中各表格的标题进行比较，
     * 让LLM选择最匹配的表格进行数据提取，而不是处理所有表格
     *
     * @param tablesResponse PDF表格提取响应，包含所有检测到的表格
     * @param columnTypes 需要提取的列类型定义
     * @param isList 指示结果是否应该是列表格式
     * @param tableName 表名（英文）
     * @param tableNameCn 表名（中文）
     * @param tableDec 表格描述
     * @return 提取的结构化数据，格式为JSON字符串
     */
    fun processIntelligentPdfTables(
        tablesResponse: TablesResponse,
        columnTypes: List<SysColumnType>,
        isList: Boolean,
        tableDesc: String,
        llmCallLogger: LlmCallLogger,
        context: Map<String, Any>? = null,
    ): String {
        // 没有检测到表格，返回空结果
        if (tablesResponse.tables.isEmpty()) {
            Log.warn("59df82a1 | PDF中未检测到表格")
            return if (isList) "[]" else "{}"
        }

        val columnInfos = columnTypes.promptColumnMetadata()

        // 确定每批次处理的表格数量，避免LLM上下文窗口溢出
        val tablesPerBatch = 50
        val allSelectedIndices = mutableListOf<Int>()

        // 分批处理表格
        val tableBatches = tablesResponse.tables.withIndex().chunked(tablesPerBatch)
        Log.info("b7d52a4c | 将${tablesResponse.tables.size}个表格分为${tableBatches.size}批处理")

        for ((batchIndex, tableBatch) in tableBatches.withIndex()) {
            Log.info("34fe876d | 处理第${batchIndex + 1}/${tableBatches.size}批表格，包含${tableBatch.size}个表格")

            // 1. 构建当前批次的表格选择提示
            val tableSelectionPrompt = buildTableSelectionPrompt(
                tableDesc = tableDesc,
                columnInfos = columnInfos,
                batchIndex = batchIndex,
                batchesSize = tableBatches.size,
                tableBatch = tableBatch
            )

            // 2. 调用LLM选择当前批次中的表格
            try {
                val selectionResponse =
                    DoubaoLlm.sendPrompt(tableSelectionPrompt, logger = llmCallLogger, context = context)

                // 3. 解析LLM返回的选择结果
                val jsonPattern =
                    "\\{.*?\"selectedTableIndices\"\\s*:\\s*(\\[.*?]).*?}".toRegex(RegexOption.DOT_MATCHES_ALL)
                val match = jsonPattern.find(selectionResponse.content)

                if (match != null) {
                    val indicesJson = match.groupValues[1]
                    try {
                        // 解析JSON数组，获取原始索引
                        val batchSelectedIndices = indicesJson.trim('[', ']').split(",").mapNotNull {
                            it.trim().toIntOrNull()?.minus(1) // 转换为0-based索引
                        }

                        if (batchSelectedIndices.isNotEmpty()) {
                            Log.info("85c72ae1 | 批次${batchIndex + 1}中选择了${batchSelectedIndices.size}个表格：$batchSelectedIndices")
                            allSelectedIndices.addAll(batchSelectedIndices)
                        } else {
                            Log.warn("7ba3d82c | 批次${batchIndex + 1}中未选择任何表格")
                        }
                    } catch (e: Exception) {
                        Log.error("f81d3e5a | 解析表格索引失败: ${e.message}", e)
                    }
                } else {
                    Log.warn("42e19f76 | 未找到匹配的JSON结果模式")
                }
            } catch (e: Exception) {
                Log.error("ae26c756 | 批次${batchIndex + 1}的表格选择失败: ${e.message}", e)
            }
        }

        Log.info("fe3a21c6 | LLM选择的所有表格索引: $allSelectedIndices")

        // 如果LLM未选择任何表格，回退到处理所有表格
        if (allSelectedIndices.isEmpty()) {
            Log.warn("6cfad2b7 | LLM未选择任何表格，将处理所有表格")
            return processPdfTables(
                tablesResponse,
                columnTypes,
                isList,
                llmCallLogger,
                context
            )
        }

        // 4. 处理选定的表格
        val allTableResults = mutableListOf<String>()

        // 去重并确保索引有效
        val distinctValidIndices = allSelectedIndices.distinct().filter { it >= 0 && it < tablesResponse.tables.size }

        distinctValidIndices.forEach { index ->
            val tableData = tablesResponse.tables[index]
            Log.info("74a5e8c3 | 处理选定的表格: ${tableData.title}")

            try {
                val prompts = LlmPromptBuilder.generatePromptForTable(tableData, columnInfos, isList)
                val totalPrompts = prompts.size

                prompts.forEachIndexed { promptIndex, prompt ->
                    val progress = ((promptIndex + 1) * 100) / totalPrompts
                    Log.info("021576c3 | 处理表格块 ${promptIndex + 1}/$totalPrompts - $progress% 完成")

                    try {
                        val llmResponse = DoubaoLlm.sendPrompt(prompt, logger = llmCallLogger, context = context)
                        val chunkResult = llmResponse.extractTagContent()
                        if (chunkResult.isNotEmpty()) {
                            allTableResults.add(chunkResult)
                        } else {
                            Log.warn("88d573f9 | 在表格块中未找到结果: ${tableData.title}")
                        }
                    } catch (e: Exception) {
                        Log.error("ae26c756 | 表格提取失败: $tableData", e)
                    }
                }
            } catch (e: Exception) {
                Log.error("adac0fc9 | 表格提取失败: $tableData", e)
            }
        }

        // 如果没有获取到任何结果，回退到处理所有表格
        if (allTableResults.isEmpty()) {
            Log.warn("d24e5f3a | 从选定表格未提取到任何数据，将处理所有表格")
            return processPdfTables(
                tablesResponse,
                columnTypes,
                isList,
                llmCallLogger,
                context
            )
        }

        // 5. 合并所有选定表格的结果
        return mergeJsonListResults(allTableResults)
    }

    fun processPdfTables(
        tablesResponse: TablesResponse,
        columnTypes: List<SysColumnType>,
        isList: Boolean,
        llmCallLogger: LlmCallLogger,
        context: Map<String, Any>? = null,
    ): String {
        // 表格处理：每个表格单独构建 prompt 并调用 LLM，最后合并所有表格的 AI response
        val allTableResults = mutableListOf<String>()
        val columnInfos = columnTypes.promptColumnMetadata()
        val totalTables = tablesResponse.tables.size
        var processedTables = 0

        for (tableData in tablesResponse.tables) {
            processedTables++
            val progress = (processedTables * 100) / totalTables
            Log.info("Processing table ${tableData.title} ($processedTables/$totalTables) - $progress% completed")
            try {
                // 获取可能包含多个提示的列表（针对长表格内容）
                val prompts = LlmPromptBuilder.generatePromptForTable(tableData, columnInfos, isList)
                val totalPrompts = prompts.size
                var processedPrompts = 0

                // 处理每个提示并合并结果
                for (prompt in prompts) {
                    processedPrompts++
                    val chunkProgress = (processedPrompts * 100) / totalPrompts
                    Log.info("  Processing chunk $processedPrompts/$totalPrompts of table ${tableData.title} - $chunkProgress% completed")
                    try {
                        val llmResponse = DoubaoLlm.sendPrompt(prompt, logger = llmCallLogger, context = context)
                        val chunkResult = llmResponse.extractTagContent()
                        if (chunkResult.isNotEmpty()) {
                            allTableResults.add(chunkResult)
                        } else {
                            Log.warn("88d573f9 | No result found in table chunk: ${tableData.title}")
                        }
                    } catch (e: Exception) {
                        Log.error("ae26c756 | Table extraction failed for table: $tableData", e)
                    }
                }
            } catch (e: Exception) {
                Log.error("adac0fc9 | Table extraction failed for table: $tableData", e)
            }
        }
        return mergeJsonListResults(allTableResults)
    }

}
