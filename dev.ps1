$server = "************"
$username = "zhongguo.gu"
$password = "datayes@667"

$t = get-date -format "MMdd_HHmm"

# Get current branch name
$currentBranch = git rev-parse --abbrev-ref HEAD

# dir name
$d = "auto-data-item-extract"

# project name (include branch name for multiple deployments)
if ($currentBranch -eq "main" -or $currentBranch -eq "master") {
    $p = "auto-data-item-extract"
} else {
    $p = "auto-data-item-extract-$currentBranch"
}

if ($args -contains "log") {
      plink -batch -pw ${password} ${username}@${server} "supervisorctl fg $p"
      exit 0
}

./gradlew quarkusBuild
if($LASTEXITCODE -ne 0) {
      exit $LASTEXITCODE
}

if ($args -contains "--full") {
      # remove lib dir under quarkus-app
      plink -batch -pw ${password} ${username}@${server} "rm -rf /datayes/project/${d}/quarkus-app/lib"
      pscp -r -pw ${password} ./build/quarkus-app ${username}@${server}:/datayes/project/${d}/
} else {
      pscp -r -pw ${password} ./build/quarkus-app/app ${username}@${server}:/datayes/project/${d}/quarkus-app
      pscp -r -pw ${password} ./build/quarkus-app/quarkus ${username}@${server}:/datayes/project/${d}/quarkus-app
}
if($LASTEXITCODE -ne 0) {
      exit $LASTEXITCODE
}

plink -batch -pw ${password} ${username}@${server} "supervisorctl restart $p"
if($LASTEXITCODE -ne 0) {
      # start if not running
      plink -batch -pw ${password} ${username}@${server} "supervisorctl start $p"
      if($LASTEXITCODE -ne 0) {
            exit $LASTEXITCODE
      }
}

plink -batch -pw ${password} ${username}@${server} "supervisorctl fg $p"
if($LASTEXITCODE -ne 0) {
      exit $LASTEXITCODE
}
