package com.datayes.quarkus.rest

import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response

/**
 * 示例提示 REST API (Example Prompt REST API)
 * 提供构建示例提示的 REST 端点
 */
@Path("/api/example-prompt")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class ExamplePromptResource(private val examplePromptService: ExamplePromptService) {

    /**
     * 根据表ID获取示例提示
     *
     * @param tableId 表ID
     * @return 示例提示内容
     */
    @GET
    @Path("/{tableId}")
    fun getExamplePrompt(@PathParam("tableId") tableId: Long): Response {
        val examplePrompt = examplePromptService.buildExamplePrompt(tableId)
        return if (examplePrompt != null) {
            Response.ok(mapOf("prompt" to examplePrompt)).build()
        } else {
            Response.status(Response.Status.NOT_FOUND)
                .entity(mapOf("message" to "未找到示例数据"))
                .build()
        }
    }
} 