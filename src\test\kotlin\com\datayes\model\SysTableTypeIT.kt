@file:Suppress("NonAsciiCharacters")

package com.datayes.model

import io.quarkus.test.junit.QuarkusTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Assumptions
import org.junit.jupiter.api.Test

@QuarkusTest
class SysTableTypeIT {

    @Test
    fun `test sys_table_type should have more than 5 records`() {
        // --- Act ---
        val count = ExtractionTask.count()

        // --- Assert ---
        assertTrue(
            count > 5,
            "The sys_table_type table should have more than 5 records, but found $count"
        )
    }

    @Test
    fun `test isTableList returns true for existing tableId 986`() {
        // --- Arrange ---
        val knownListTableId = 986L

        // Optional: Check if the record actually exists before asserting its property.
        // This makes the test failure clearer if the assumed data is missing.
        val exists = ExtractionTask.count("tableId", knownListTableId) > 0
        Assumptions.assumeTrue(
            exists,
            "ASSUMPTION FAILED: Record with tableId $knownListTableId not found in the database."
        )

        // --- Act ---
        val result = ExtractionTask.isTableList(knownListTableId)

        // --- Assert ---
        assertTrue(
            result,
            "isTableList should return true for the pre-existing tableId $knownListTableId, which is expected to be a list."
        )
    }

    @Test
    fun `test isTableList returns false for existing tableId 1134`() {
        // --- Arrange ---
        val knownNonListTableId = 1134L

        // Optional: Check if the record actually exists before asserting its property.
        val exists = ExtractionTask.count("tableId", knownNonListTableId) > 0
        Assumptions.assumeTrue(
            exists,
            "ASSUMPTION FAILED: Record with tableId $knownNonListTableId not found in the database."
        )

        // --- Act ---
        val result = ExtractionTask.isTableList(knownNonListTableId)

        // --- Assert ---
        assertFalse(
            result,
            "isTableList should return false for the pre-existing tableId $knownNonListTableId, which is expected NOT to be a list."
        )
    }

    @Test
    fun `test equ_ipo_lotto table should have ext_type as 表格`() {
        // --- Arrange ---
        val tableName = "equ_ipo_lotto"

        // Check if the record exists
        val record = ExtractionTask.find("tableName", tableName).firstResult()
        Assumptions.assumeTrue(
            record != null,
            "ASSUMPTION FAILED: Record with tableName $tableName not found in the database."
        )

        // --- Assert ---
        assertEquals(
            "表格",
            record?.extType,
            "The table $tableName should have ext_type set to '表格'"
        )
    }
}
