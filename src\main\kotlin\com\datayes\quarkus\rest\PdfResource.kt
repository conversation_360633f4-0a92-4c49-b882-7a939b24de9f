package com.datayes.quarkus.rest

import com.datayes.llm.LlmCallLogger
import com.datayes.model.LlmResponse
import com.datayes.model.SysColumnType
import com.datayes.model.SysDataAnno
import com.datayes.model.ExtractionTask
import com.datayes.quarkus.client.ExtractContentRequest
import com.datayes.quarkus.client.RemotePdfContentClient
import com.datayes.quarkus.client.RemotePdfTablesClient
import com.datayes.quarkus.client.TablesResponse
import com.datayes.quarkus.service.PdfTableExtractor.processIntelligentPdfTables
import com.datayes.quarkus.service.PdfTextExtractor.parsePdfTextToJson
import io.quarkus.logging.Log
import jakarta.transaction.Transactional
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import org.eclipse.microprofile.rest.client.inject.RestClient
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * PDF 资源类 (PDF Resource)
 * 提供 PDF 文本提取的 REST API
 */
@Path("/api/pdf")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class PdfResource(
    @RestClient private val pdfContentClient: RemotePdfContentClient,
    @RestClient private val pdfTablesClient: RemotePdfTablesClient,
    private val examplePromptService: ExamplePromptService,
    private val llmCallLogger: LlmCallLogger,
) {

    /**
     * 简单的LRU缓存，用于存储最近10个PDF表格提取结果
     * 键：PDF URL
     * 值：TablesResponse
     */
    private val pdfTablesCache = object : LinkedHashMap<String, TablesResponse>(16, 0.75f, true) {
        override fun removeEldestEntry(eldest: Map.Entry<String, TablesResponse>): Boolean {
            return size > 10 // 限制缓存大小为10个项目
        }
    }

    /**
     * 处理表数据提取的核心逻辑
     *
     * @param id 数据标注ID
     * @return 表数据提取结果
     */
    private fun extractSingle(
        task: ExtractionTask,
        columns: List<SysColumnType>,
        id: Long
    ): String {
        val (dataAnno, annoId, taskId) = extractDataForTask(id, task)

        val examplePrompt = examplePromptService.buildExamplePrompt(taskId = dataAnno.taskId!!)

        val context = mapOf(
            "id" to id,
            "annoId" to annoId,
            "taskId" to taskId,
        )

        // 2. Extract phase - no transaction needed
        val pdfUrl = dataAnno.reportAddress?.takeIf { it.isNotBlank() }?.let { "https://bigdata-s3.datayes.com$it" }
            ?: error("数据配置中未找到PDF地址")

        Log.info("15f06041 | pdfUrl | $pdfUrl")

        val isList = task.isList == true
        val isTable = task.isTable()

        val extractedResult = if (isTable) {
            // 尝试从缓存获取结果，如果缓存未命中则调用API并更新缓存
            val tablesResponse = synchronized(pdfTablesCache) {
                pdfTablesCache.getOrPut(pdfUrl) {
                    Log.info("ddbd3cb7 | PDF表格缓存未命中，从API获取: $pdfUrl")
                    pdfTablesClient.retrievePdfTables(pdfUrl)
                }
            }
            val tableDesc = task.extDesc
            if (tableDesc.isNullOrBlank()) {
                error("15f06041 | extDesc is null or blank")
            }
            processIntelligentPdfTables(
                tablesResponse = tablesResponse,
                columnTypes = columns,
                isList = isList,
                tableDesc = tableDesc,
                llmCallLogger = llmCallLogger,
                context = context
            )
        } else {
            val lines = pdfContentClient.extractContent(ExtractContentRequest(pdfUrl))
            parsePdfTextToJson(lines, columns, isList, examplePrompt, llmCallLogger, context)
        }

        // 3. Save phase - separate transaction
        saveOrUpdateResponse(id, extractedResult, isTable, annoId, taskId)

        return extractedResult
    }

    @Transactional
    fun extractDataForTask(id: Long, task: ExtractionTask): Triple<SysDataAnno, String, Long> {
        val dataAnno = SysDataAnno.findById(id) ?: error("未找到相关数据标注数据")
        if (dataAnno.isExa == true) {
            error("数据配置中已标记为示例数据")
        }

        val annoId = dataAnno.annoId ?: error("b8ad649a | annoId is null or $id")

        val taskId = task.id ?: error("15f06041 | taskId is null or $id")
        return Triple(dataAnno, annoId, taskId)
    }

    @GET
    @Path("/extract-by-task-id")
    @Produces(MediaType.TEXT_PLAIN)
    fun extractByTaskId(@QueryParam("taskId") taskId: Long): BatchProcessingResponse {
        return extractMutiple(taskId, SysDataAnno.findByTaskId(taskId))
    }

    /**
     * Data class to hold configuration data for table extraction
     */
    data class ConfigData(
        val dataAnno: SysDataAnno,
        val columnTypes: List<SysColumnType>,
        val isList: Boolean,
        val isTable: Boolean,
        val tableName: String?,
        val tableNameCn: String?,
        val annoId: String,
        val tableId: Long,
        val tableDesc: String?
    )

    @Transactional
    fun saveOrUpdateResponse(
        dataAnnoId: Long,
        extractedResult: String,
        isTable: Boolean,
        annoId: String,
        taskId: Long
    ) {

        val now = LocalDateTime.now()

        if (isTable && annoId.isNotBlank()) {
            val sysDataAnnoList = SysDataAnno.findByTaskIdAndAnnoId(taskId, annoId)
            val sysDataAnnoIds = sysDataAnnoList.map { it.id }
            val llmResponses = LlmResponse.find("sysDataAnnoId in ?1", sysDataAnnoIds)
            println(llmResponses)
        }

        val existingResponse = LlmResponse.find("sysDataAnnoId", dataAnnoId).list()
        if (existingResponse.size > 1) {
            error("存在多条记录, annoId=$dataAnnoId")
        }

        if (existingResponse.isEmpty()) {
            LlmResponse(
                taskId = taskId,
                sysDataAnnoId = dataAnnoId, // new release should use annoId but the data type is not right todo wujie
                responseJson = extractedResult,
                fileAddress = "",
                createTime = now,
                updateTime = now,
            ).persist()
        } else {
            val single = existingResponse.single()
            single.responseJson = extractedResult
            single.updateTime = LocalDateTime.now()
            single.persist()
        }
    }

    /**
     * 处理数据提取的通用方法 (Common method for data extraction processing)
     *
     * @return 批量处理结果
     */
    private fun extractMutiple(taskId: Long, dataAnnos: List<SysDataAnno>): BatchProcessingResponse {
        // 1. 获取SysDataAnno记录
        if (dataAnnos.isEmpty()) {
            error("未找到任何需要处理的数据记录 (No data records found to process)")
        }

        // Filter dataAnnos to only keep the first one with each unique annoId
        val filteredDataAnnos = dataAnnos.groupBy { it.annoId }
            .mapValues { (_, annos) -> annos.first() }
            .values
            .toList()

        val extractionTask = ExtractionTask.findById(taskId) ?: error("cf9049f1 | 未找到相关配置数据")
        val sysColumnTypes = SysColumnType.findByTaskId(taskId)
        if (sysColumnTypes.isEmpty()) {
            error("e3522436 | 未找到相关字段配置数据")
        }

        // 2. 准备并发处理结果统计 (Prepare concurrent processing statistics)
        val threadPool = Executors.newFixedThreadPool(20)
        val concurrentResults = ConcurrentHashMap<String, Int>()
        concurrentResults["总记录数 (Total Records)"] = filteredDataAnnos.size
        concurrentResults["成功处理 (Success)"] = 0
        concurrentResults["处理失败 (Failed)"] = 0

        val processingResultsList = mutableListOf<String>()
        val latch = CountDownLatch(filteredDataAnnos.size)

        // 3. 使用线程池并行处理每个记录 (Use thread pool to process records in parallel)
        filteredDataAnnos.forEach { dataAnno ->
            threadPool.submit {
                try {
                    val dataAnnoId = dataAnno.id
                    if (dataAnnoId == null) {
                        synchronized(processingResultsList) {
                            processingResultsList.add("ID为空 (Empty ID)")
                        }
                        concurrentResults.compute("处理失败 (Failed)") { _, v -> (v ?: 0) + 1 }
                    } else {
                        val result = runCatching { extractSingle(extractionTask, sysColumnTypes, dataAnnoId) }
                        when {
                            result.isSuccess -> {
                                concurrentResults.compute("成功处理 (Success)") { _, v -> (v ?: 0) + 1 }
                                synchronized(processingResultsList) {
                                    processingResultsList.add("ID: $dataAnnoId - 成功 (Success)")
                                }
                            }

                            result.isFailure -> {
                                concurrentResults.compute("处理失败 (Failed)") { _, v -> (v ?: 0) + 1 }
                                synchronized(processingResultsList) {
                                    processingResultsList.add("ID: $dataAnnoId - 失败: ${result.exceptionOrNull()?.localizedMessage})")
                                }
                            }
                        }
                    }
                } finally {
                    latch.countDown()
                }
            }
        }

        // 等待所有任务完成 (Wait for all tasks to complete)
        latch.await(30, TimeUnit.MINUTES)
        threadPool.shutdown()

        // 4. 获取处理结果 (Get processing results)
        val results = concurrentResults.toMap()

        // 返回处理结果
        val batchProcessingResponse = BatchProcessingResponse(
            statistics = results,
            details = processingResultsList
        )

        return batchProcessingResponse
    }

    /**
     * 批量处理响应
     * 用于返回批量处理任务的结果
     */
    data class BatchProcessingResponse(
        val statistics: Map<String, Int>,
        val details: List<String>,
    )

    /**
     * 直接提取请求的数据类
     * 包含直接传入的参数，不需要从数据库查询
     */
    data class DirectExtractionRequest(
        val s3Url: String,
        val extractionTask: DirectExtractionTask,
        val columnTypes: List<DirectSysColumnType>
    )

    /**
     * 简化的ExtractionTask，只包含必要字段
     */
    data class DirectExtractionTask(
        val extType: String,      // 抽取类型 0-文本，1-表格，2-图片
        val extDesc: String?,     // 数据名称以及简单的规则
        val isList: Boolean       // 是否抽取多条数据
    )

    /**
     * 简化的SysColumnType，只包含必要字段
     */
    data class DirectSysColumnType(
        val columnName: String,    // 字段中文名
        val description: String?,  // 字段描述
        val rule: String?         // 抽取规则
    )

    /**
     * 直接提取响应
     */
    data class DirectExtractionResponse(
        val extractedData: String,     // LLM返回的提取结果
        val success: Boolean,          // 是否成功
        val errorMessage: String? = null
    )

    /**
     * 直接提取API - 不从数据库查询配置，直接接收参数
     * 
     * @param request 包含s3Url、extractionTask和columnTypes的请求体
     * @return 直接返回LLM提取结果，不保存到LlmResponse表
     */
    @POST
    @Path("/extract-direct")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    fun extractDirect(request: DirectExtractionRequest): DirectExtractionResponse {
        return try {
            val pdfUrl = request.s3Url
            val task = request.extractionTask
            val columns = request.columnTypes
            
            Log.info("b4e7f2a9 | 直接提取API调用 - pdfUrl: $pdfUrl")
            
            // 转换DirectSysColumnType为SysColumnType以复用现有逻辑
            val sysColumnTypes = columns.map { col ->
                SysColumnType(
                    columnName = col.columnName,
                    description = col.description,
                    rule = col.rule
                )
            }
            
            val isList = task.isList
            val isTable = task.extType.trim() == "1"
            
            // 构建上下文信息用于日志记录
            val context = mapOf(
                "directExtraction" to true,
                "s3Url" to pdfUrl,
                "extType" to task.extType,
                "isList" to isList,
                "isTable" to isTable
            )
            
            val extractedResult = if (isTable) {
                // 表格提取
                val tablesResponse = synchronized(pdfTablesCache) {
                    pdfTablesCache.getOrPut(pdfUrl) {
                        Log.info("5c8d1a4e | PDF表格缓存未命中，从API获取: $pdfUrl")
                        pdfTablesClient.retrievePdfTables(pdfUrl)
                    }
                }
                
                val tableDesc = task.extDesc
                if (tableDesc.isNullOrBlank()) {
                    error("extDesc不能为空")
                }
                
                processIntelligentPdfTables(
                    tablesResponse = tablesResponse,
                    columnTypes = sysColumnTypes,
                    isList = isList,
                    tableDesc = tableDesc,
                    llmCallLogger = llmCallLogger,
                    context = context
                )
            } else {
                // 文本提取
                val lines = pdfContentClient.extractContent(ExtractContentRequest(pdfUrl))
                parsePdfTextToJson(
                    lines = lines, 
                    columnTypes = sysColumnTypes, 
                    isList = isList, 
                    examplePrompt = null, // 直接提取不使用示例
                    llmCallLogger = llmCallLogger,
                    context = context
                )
            }
            
            Log.info("3f9a6d2c | 直接提取成功")
            DirectExtractionResponse(
                extractedData = extractedResult,
                success = true
            )
            
        } catch (e: Exception) {
            Log.error("a8c5f7b1 | 直接提取失败: ${e.message}", e)
            DirectExtractionResponse(
                extractedData = "",
                success = false,
                errorMessage = e.message
            )
        }
    }
}
